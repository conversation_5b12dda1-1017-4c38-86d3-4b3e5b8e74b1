import type { ILocaleContext } from '@bika/contents/i18n';
import type { CallAgentAction } from '@bika/types/automation/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { ToSchemaBOInput } from '@bika/ui/unit/types-form/to-schema-bo-input';
import { NodeResourceSelect } from '../../../node/types-form/node-resource-select';

interface CallAgentActionBOInputProps {
  value: CallAgentAction;
  onChange: (newVal: CallAgentAction) => void;
  locale: ILocaleContext;
}

export const CallAgentActionBOInput = (props: CallAgentActionBOInputProps) => {
  const { value, locale } = props;
  const api = useNodeResourceApiContext();
  const { t } = locale;
  return (
    <>
      <NodeResourceSelect
        resourceType="AI"
        resourceId={value.input.agentId}
        required
        setResourceId={(newVal) => {
          props.onChange({
            ...value,
            input: {
              ...value.input,
              agentId: newVal,
            },
          });
        }}
        locale={locale}
        label={t.automation.action.call_agent.agent_id_subject}
      />
      {/* <ToSchemaListBOInput
        label={t.automation.action.call_agent.recipient}
        value={value.input.to ?? []}
        max={1}
        required
        onChange={(newVal) => {
          const newTo = newVal as To[];
          props.onChange({
            ...value,
            input: {
              ...value.input,
              to: newTo,
            },
          });
        }}
        types={['UNIT_MEMBER']}
        locale={locale}
        actionId={value.id}
      /> */}

      <FormLabel required={true}>{t.automation.action.call_agent.recipient}</FormLabel>

      <FormHelperText sx={{ mb: 1 }}>{props.helpText}</FormHelperText>

      <ToSchemaBOInput
        types={['UNIT_MEMBER']}
        // label={t.automation.action.call_agent.recipient}
        value={
          value.input.to?.[0] ?? {
            type: 'UNIT_MEMBER',
          }
        }
        multiple={false}
        onChange={(newVal) => {
          console.log('vv');
          // const newTo = newVal as To[];
          props.onChange({
            ...value,
            input: {
              ...value.input,
              to: [newVal],
            },
          });
        }}
        locale={locale}
        api={api}
      />

      <VariablesTextInput
        locale={locale}
        label={t.automation.action.call_agent.message_subject}
        value={value.input.message}
        required
        onChange={(newVal) => {
          props.onChange({
            ...value,
            input: {
              ...value.input,
              message: newVal,
            },
          });
        }}
      />
    </>
  );
};
